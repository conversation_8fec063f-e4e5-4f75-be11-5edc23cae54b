# 移除src目录结构任务

## 任务概述
将 src/code_standard_mcp 目录下的所有文件和子目录移动到项目根目录，删除 src 目录，简化项目结构。

## 执行计划
1. 移动核心文件：__init__.py、main.py
2. 移动子目录：config、models、services、storage
3. 删除整个 src 目录
4. 更新 pyproject.toml 配置

## 执行结果
✅ 成功移动所有文件和目录到根目录
✅ 成功删除 src 目录
✅ 更新 pyproject.toml 添加包配置

## 最终项目结构
```
./
├── __init__.py
├── main.py
├── config/
├── models/
├── services/
├── storage/
├── pyproject.toml
├── README.md
├── Mcp.md
└── uv.lock
```

## 配置更新
- 在 pyproject.toml 中添加了 [project.scripts] 和 [tool.hatch.build.targets.wheel] 配置
- 确保包能正确构建和安装

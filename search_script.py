#!/usr/bin/env python3
"""
搜索项目中所有包含 'code-standard-mcp' 的文件
"""
import os
import re
from pathlib import Path

def search_files(directory, pattern):
    """搜索目录中包含指定模式的文件"""
    results = []
    
    # 要忽略的目录和文件
    ignore_dirs = {'.git', '__pycache__', '.pytest_cache', 'node_modules', '.venv', 'venv'}
    ignore_files = {'.pyc', '.pyo', '.pyd', '.so', '.dll', '.exe'}
    
    for root, dirs, files in os.walk(directory):
        # 过滤要忽略的目录
        dirs[:] = [d for d in dirs if d not in ignore_dirs]
        
        for file in files:
            file_path = Path(root) / file
            
            # 跳过二进制文件和特定扩展名
            if any(file.endswith(ext) for ext in ignore_files):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                # 搜索模式
                matches = []
                for line_num, line in enumerate(content.splitlines(), 1):
                    if pattern in line:
                        matches.append((line_num, line.strip()))
                
                if matches:
                    results.append((str(file_path), matches))
                    
            except (UnicodeDecodeError, PermissionError, OSError):
                # 跳过无法读取的文件
                continue
    
    return results

if __name__ == "__main__":
    # 搜索当前目录
    current_dir = "."
    search_pattern = "code-standard-mcp"
    
    print(f"搜索包含 '{search_pattern}' 的文件...")
    print("=" * 50)
    
    results = search_files(current_dir, search_pattern)
    
    if results:
        for file_path, matches in results:
            print(f"\n文件: {file_path}")
            for line_num, line in matches:
                print(f"  行 {line_num}: {line}")
    else:
        print("未找到包含该模式的文件。")
    
    print(f"\n搜索完成。共找到 {len(results)} 个文件。")

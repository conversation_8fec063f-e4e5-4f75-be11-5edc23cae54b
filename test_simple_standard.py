#!/usr/bin/env python3
"""
测试简化的硬编码编码规范功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models.standard import StandardSelector
from services.standard_fetcher import standard_fetcher


async def test_simple_standard():
    """测试简化的硬编码规范"""
    
    test_cases = [
        StandardSelector(language="python"),
        StandardSelector(language="javascript", framework="react"),
        StandardSelector(language="java", framework="spring"),
        StandardSelector(language="unknown"),
    ]
    
    print("=== 测试简化的硬编码编码规范功能 ===\n")
    
    for i, selector in enumerate(test_cases, 1):
        print(f"测试 {i}: {selector.language}" + 
              (f" ({selector.framework})" if selector.framework else ""))
        
        try:
            result = await standard_fetcher.fetch_standard(selector)
            
            if result["success"]:
                content_preview = result["content"][:100] + "..." if len(result["content"]) > 100 else result["content"]
                print(f"✅ 成功获取规范")
                print(f"   来源: {result['source']}")
                print(f"   内容预览: {content_preview}")
            else:
                print(f"❌ 获取失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        print("-" * 50)


if __name__ == "__main__":
    asyncio.run(test_simple_standard())

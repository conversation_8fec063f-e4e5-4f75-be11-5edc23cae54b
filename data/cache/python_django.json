{"metadata": {"selector": {"language": "python", "framework": "django", "version": null, "environment": null, "domain": null}, "fetch_date": "2025-07-04 16:34:50.499228", "source_url": "https://api.example.com/standards?language=python&framework=django", "content_hash": "bc6b2194320372afbf25d245e07370f4"}, "content": "# Python Django 编码规范\n\n## 1. Django 特定约定\n\n### 模型命名\n- 模型类使用单数形式\n- 例如：`User`, `Article`, `Category`\n\n### 视图命名\n- 基于类的视图使用 CapWords\n- 基于函数的视图使用小写加下划线\n- 例如：`UserListView`, `user_detail`\n\n### URL 命名\n- 使用小写字母和连字符\n- 例如：`user-list`, `article-detail`\n\n## 2. 模型定义\n\n```python\nclass Article(models.Model):\n    title = models.CharField(max_length=200, verbose_name=\"标题\")\n    content = models.TextField(verbose_name=\"内容\")\n    created_at = models.DateTimeField(auto_now_add=True, verbose_name=\"创建时间\")\n\n    class Meta:\n        verbose_name = \"文章\"\n        verbose_name_plural = \"文章\"\n        ordering = ['-created_at']\n\n    def __str__(self):\n        return self.title\n```\n\n## 3. 视图定义\n\n```python\nclass ArticleListView(ListView):\n    model = Article\n    template_name = 'articles/list.html'\n    context_object_name = 'articles'\n    paginate_by = 10\n```\n\n## 4. 表单定义\n\n```python\nclass ArticleForm(forms.ModelForm):\n    class Meta:\n        model = Article\n        fields = ['title', 'content']\n        widgets = {\n            'content': forms.Textarea(attrs={'rows': 10}),\n        }\n```\n"}
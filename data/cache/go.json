{"metadata": {"selector": {"language": "go", "framework": null, "version": null, "environment": null, "domain": null}, "fetch_date": "2025-07-04 16:34:50.551732", "source_url": "https://api.example.com/standards?language=go", "content_hash": "7c050b43090fb1ee814ca388f089348f"}, "content": "# Go 编码规范\n\n## 1. 命名约定\n\n### 包名\n- 使用小写字母\n- 简短且有意义\n- 例如：`user`, `http`, `json`\n\n### 变量和函数\n- 使用 camelCase\n- 导出的标识符首字母大写\n- 例如：`userName`, `GetUser()`\n\n## 2. 函数定义\n\n```go\n// 简单函数\nfunc calculateTotal(items []Item) float64 {\n    var total float64\n    for _, item := range items {\n        total += item.Price\n    }\n    return total\n}\n\n// 多返回值\nfunc getUser(id int) (*User, error) {\n    user, err := db.FindUser(id)\n    if err != nil {\n        return nil, fmt.Errorf(\"查找用户失败: %w\", err)\n    }\n    return user, nil\n}\n```\n\n## 3. 结构体定义\n\n```go\ntype User struct {\n    ID    int    `json:\"id\"`\n    Name  string `json:\"name\"`\n    Email string `json:\"email\"`\n}\n\nfunc (u *User) String() string {\n    return fmt.Sprintf(\"User{ID: %d, Name: %s}\", u.ID, u.Name)\n}\n```\n\n## 4. 错误处理\n\n```go\nif err != nil {\n    log.Printf(\"处理失败: %v\", err)\n    return fmt.Errorf(\"操作失败: %w\", err)\n}\n```\n"}
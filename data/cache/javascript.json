{"metadata": {"selector": {"language": "javascript", "framework": null, "version": null, "environment": null, "domain": null}, "fetch_date": "2025-07-04 16:34:50.515609", "source_url": "https://api.example.com/standards?language=javascript", "content_hash": "224ec64688414cf575eecd1b725dd731"}, "content": "# JavaScript 编码规范\n\n## 1. 代码格式\n\n### 缩进\n- 使用 2 个空格进行缩进\n- 不要使用制表符\n\n### 分号\n- 总是使用分号结束语句\n\n### 引号\n- 优先使用单引号，除非字符串中包含单引号\n\n## 2. 命名约定\n\n### 变量和函数\n- 使用 camelCase\n- 例如：`userName`, `getUserInfo()`\n\n### 常量\n- 使用 UPPER_SNAKE_CASE\n- 例如：`MAX_SIZE`, `API_URL`\n\n### 类名\n- 使用 PascalCase\n- 例如：`UserManager`, `ApiClient`\n\n## 3. 函数定义\n\n```javascript\n// 函数声明\nfunction calculateTotal(items) {\n  return items.reduce((sum, item) => sum + item.price, 0);\n}\n\n// 箭头函数\nconst formatPrice = (price) => {\n  return `$${price.toFixed(2)}`;\n};\n\n// 异步函数\nasync function fetchUserData(userId) {\n  try {\n    const response = await fetch(`/api/users/${userId}`);\n    return await response.json();\n  } catch (error) {\n    console.error('获取用户数据失败:', error);\n    throw error;\n  }\n}\n```\n\n## 4. 对象和数组\n\n```javascript\n// 对象\nconst user = {\n  name: 'John Doe',\n  email: '<EMAIL>',\n  age: 30\n};\n\n// 数组\nconst colors = ['red', 'green', 'blue'];\n\n// 解构赋值\nconst { name, email } = user;\nconst [firstColor, ...restColors] = colors;\n```\n\n## 5. 错误处理\n\n```javascript\ntry {\n  const result = riskyOperation();\n  return result;\n} catch (error) {\n  console.error('操作失败:', error.message);\n  throw new Error('处理失败');\n}\n```\n"}
{"metadata": {"selector": {"language": "python", "framework": "flask", "version": null, "environment": null, "domain": null}, "fetch_date": "2025-07-04 16:34:50.513227", "source_url": "https://api.example.com/standards?language=python&framework=flask", "content_hash": "a333bc71baa56fdcdffbcfb41bf212a7"}, "content": "# Python 编码规范\n\n## 1. 代码布局\n\n### 缩进\n- 使用 4 个空格进行缩进，不要使用制表符\n- 续行应该与其包装元素对齐\n\n### 行长度\n- 限制所有行的最大长度为 79 个字符\n- 文档字符串或注释的行长度限制为 72 个字符\n\n### 空行\n- 顶级函数和类定义前后用两个空行分隔\n- 类内方法定义前后用一个空行分隔\n\n## 2. 导入\n\n### 导入顺序\n1. 标准库导入\n2. 相关第三方库导入\n3. 本地应用/库导入\n\n### 导入格式\n```python\n# 正确\nimport os\nimport sys\nfrom typing import List, Dict\n\n# 错误\nimport os, sys\n```\n\n## 3. 命名约定\n\n### 变量和函数\n- 使用小写字母，单词间用下划线分隔\n- 例如：`user_name`, `get_user_info()`\n\n### 类名\n- 使用 CapWords 约定（驼峰命名）\n- 例如：`UserManager`, `DatabaseConnection`\n\n### 常量\n- 使用全大写字母，单词间用下划线分隔\n- 例如：`MAX_SIZE`, `DEFAULT_TIMEOUT`\n\n## 4. 注释和文档字符串\n\n### 文档字符串\n```python\ndef function_name(param1: str, param2: int) -> bool:\n    \"\"\"\n    函数的简短描述。\n\n    Args:\n        param1: 参数1的描述\n        param2: 参数2的描述\n\n    Returns:\n        返回值的描述\n    \"\"\"\n    pass\n```\n\n### 行内注释\n- 与代码至少间隔两个空格\n- 使用完整的句子，首字母大写\n\n## 5. 表达式和语句\n\n### 比较\n```python\n# 正确\nif foo is not None:\nif not seq:\n\n# 错误\nif foo != None:\nif len(seq) == 0:\n```\n\n### 异常处理\n```python\n# 正确\ntry:\n    process_data()\nexcept ValueError as e:\n    logger.error(f\"处理数据时出错: {e}\")\n```\n\n\n## Flask 特定约定\n\n### 应用结构\n```python\nfrom flask import Flask, request, jsonify\n\napp = Flask(__name__)\n\***********('/api/users', methods=['GET'])\ndef get_users():\n    users = User.query.all()\n    return jsonify([user.to_dict() for user in users])\n\***********('/api/users', methods=['POST'])\ndef create_user():\n    data = request.get_json()\n    user = User(**data)\n    db.session.add(user)\n    db.session.commit()\n    return jsonify(user.to_dict()), 201\n```\n"}
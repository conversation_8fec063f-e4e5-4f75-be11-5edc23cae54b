{"metadata": {"selector": {"language": "typescript", "framework": null, "version": null, "environment": null, "domain": null}, "fetch_date": "2025-07-04 16:34:50.548732", "source_url": "https://api.example.com/standards?language=typescript", "content_hash": "4d8fa88a1057c850e4bf2fbba2150426"}, "content": "# TypeScript 编码规范\n\n## 1. 类型定义\n\n### 接口\n```typescript\ninterface User {\n  id: number;\n  name: string;\n  email: string;\n  isActive?: boolean;\n}\n```\n\n### 类型别名\n```typescript\ntype Status = 'pending' | 'approved' | 'rejected';\ntype UserWithStatus = User & { status: Status };\n```\n\n## 2. 函数定义\n\n```typescript\n// 函数声明\nfunction calculateTotal(items: Item[]): number {\n  return items.reduce((sum, item) => sum + item.price, 0);\n}\n\n// 箭头函数\nconst formatPrice = (price: number): string => {\n  return `$${price.toFixed(2)}`;\n};\n\n// 异步函数\nasync function fetchUser(id: number): Promise<User> {\n  const response = await fetch(`/api/users/${id}`);\n  return response.json();\n}\n```\n\n## 3. 类定义\n\n```typescript\nclass UserService {\n  private apiUrl: string;\n\n  constructor(apiUrl: string) {\n    this.apiUrl = apiUrl;\n  }\n\n  async getUser(id: number): Promise<User> {\n    const response = await fetch(`${this.apiUrl}/users/${id}`);\n    return response.json();\n  }\n}\n```\n\n## 4. 泛型\n\n```typescript\ninterface ApiResponse<T> {\n  data: T;\n  status: number;\n  message: string;\n}\n\nfunction processResponse<T>(response: ApiResponse<T>): T {\n  if (response.status !== 200) {\n    throw new Error(response.message);\n  }\n  return response.data;\n}\n```\n"}
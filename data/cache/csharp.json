{"metadata": {"selector": {"language": "csharp", "framework": null, "version": null, "environment": null, "domain": null}, "fetch_date": "2025-07-04 16:34:50.549732", "source_url": "https://api.example.com/standards?language=csharp", "content_hash": "ff2c4e5409cfe480820be5878b424f6a"}, "content": "# C# 编码规范\n\n## 1. 命名约定\n\n### 类和方法\n- 使用 PascalCase\n- 例如：`UserManager`, `GetUserInfo()`\n\n### 变量和字段\n- 私有字段使用 camelCase，可选择 _ 前缀\n- 公共属性使用 PascalCase\n- 例如：`_userName`, `UserName`\n\n## 2. 类定义\n\n```csharp\npublic class User\n{\n    private string _name;\n    private string _email;\n\n    public string Name\n    {\n        get => _name;\n        set => _name = value;\n    }\n\n    public string Email\n    {\n        get => _email;\n        set => _email = value;\n    }\n\n    public User(string name, string email)\n    {\n        _name = name;\n        _email = email;\n    }\n}\n```\n\n## 3. 异常处理\n\n```csharp\ntry\n{\n    ProcessData();\n}\ncatch (ArgumentException ex)\n{\n    _logger.LogError(ex, \"参数错误\");\n    throw new ProcessingException(\"处理失败\", ex);\n}\nfinally\n{\n    Cleanup();\n}\n```\n"}
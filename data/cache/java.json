{"metadata": {"selector": {"language": "java", "framework": null, "version": null, "environment": null, "domain": null}, "fetch_date": "2025-07-04 16:34:50.534734", "source_url": "https://api.example.com/standards?language=java", "content_hash": "f603bb81716585d64bc1b131a0a9f3e9"}, "content": "# Java 编码规范\n\n## 1. 代码格式\n\n### 缩进\n- 使用 4 个空格进行缩进\n- 不要使用制表符\n\n### 大括号\n- 左大括号不换行\n- 右大括号独占一行\n\n```java\npublic class Example {\n    public void method() {\n        if (condition) {\n            // 代码\n        }\n    }\n}\n```\n\n## 2. 命名约定\n\n### 类名\n- 使用 PascalCase\n- 例如：`UserManager`, `DatabaseConnection`\n\n### 方法和变量\n- 使用 camelCase\n- 例如：`getUserName()`, `totalAmount`\n\n### 常量\n- 使用 UPPER_SNAKE_CASE\n- 例如：`MAX_SIZE`, `DEFAULT_TIMEOUT`\n\n### 包名\n- 使用小写字母，用点分隔\n- 例如：`com.company.project.module`\n\n## 3. 类定义\n\n```java\npublic class User {\n    private String name;\n    private String email;\n\n    public User(String name, String email) {\n        this.name = name;\n        this.email = email;\n    }\n\n    public String getName() {\n        return name;\n    }\n\n    public void setName(String name) {\n        this.name = name;\n    }\n}\n```\n\n## 4. 异常处理\n\n```java\ntry {\n    processData();\n} catch (IOException e) {\n    logger.error(\"处理数据时发生IO错误\", e);\n    throw new ProcessingException(\"数据处理失败\", e);\n} finally {\n    cleanup();\n}\n```\n"}
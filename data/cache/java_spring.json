{"metadata": {"selector": {"language": "java", "framework": "spring", "version": null, "environment": null, "domain": null}, "fetch_date": "2025-07-04 16:42:18.619998", "source_url": "https://api.example.com/standards?language=java&framework=spring", "content_hash": "1c1a9d6e655b675a1250496ccb83894f"}, "content": "# 通用编码规范\n\n## 1. 代码组织\n\n### 文件结构\n- 保持文件大小适中，避免过长的文件\n- 相关功能放在同一个模块中\n- 使用清晰的目录结构\n\n### 函数设计\n- 函数应该只做一件事\n- 函数名应该清楚地表达其功能\n- 避免过长的参数列表\n\n## 2. 命名规范\n\n### 通用原则\n- 使用有意义的名称\n- 避免缩写和简写\n- 保持命名一致性\n\n### 变量命名\n- 使用描述性的名称\n- 布尔变量使用 is/has/can 等前缀\n\n### 函数命名\n- 使用动词开头\n- 清楚表达函数的作用\n\n## 3. 注释规范\n\n### 何时写注释\n- 解释复杂的业务逻辑\n- 说明算法的思路\n- 标注重要的假设和限制\n\n### 注释格式\n- 保持注释简洁明了\n- 及时更新注释内容\n- 避免显而易见的注释\n\n## 4. 错误处理\n\n### 异常处理\n- 捕获具体的异常类型\n- 提供有意义的错误信息\n- 记录错误日志\n\n### 返回值检查\n- 检查函数返回值\n- 处理边界情况\n- 验证输入参数\n\n## 5. 性能考虑\n\n### 算法复杂度\n- 选择合适的数据结构\n- 避免不必要的循环嵌套\n- 考虑时间和空间复杂度\n\n### 资源管理\n- 及时释放资源\n- 避免内存泄漏\n- 合理使用缓存\n"}
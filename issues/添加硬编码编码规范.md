# 添加硬编码编码规范功能

## 任务背景
由于获取编码规范的URL还没开发好，需要先在 `services/standard_fetcher.py` 中添加硬编码规范功能，确保无论什么条件都能获取到相应的编码规范。

## 实施方案（已简化）
在 `_fetch_from_remote` 方法中添加硬编码检查，不论什么条件都返回同一个通用编码规范。

## 具体实现

### 1. 修改 `_fetch_from_remote` 方法
- 在方法开始处添加硬编码规范检查
- 优先返回硬编码内容
- 保留原有远程获取逻辑作为备用

### 2. 简化的 `_get_hardcoded_standard` 方法
- 不论什么条件都返回同一个通用编码规范
- 调用 `_get_general_standard()` 方法

### 3. 通用编码规范内容
- `_get_general_standard()` - 包含通用编码规范，涵盖：
  - 代码组织（文件结构、函数设计）
  - 命名规范（通用原则、变量命名、函数命名）
  - 注释规范（何时写注释、注释格式）
  - 错误处理（异常处理、返回值检查）
  - 性能考虑（算法复杂度、资源管理）

## 测试结果
✅ 所有语言和框架组合都返回同一个通用编码规范
✅ 来源显示为 "remote"（硬编码内容）
✅ 保持了现有代码结构和接口不变
✅ 实现简单，易于维护

## 文件修改
- `services/standard_fetcher.py` - 添加简化的硬编码规范功能

## 后续计划
当真实的API端点开发完成后，可以简单地移除 `_get_hardcoded_standard` 调用，恢复完全的远程获取功能。
